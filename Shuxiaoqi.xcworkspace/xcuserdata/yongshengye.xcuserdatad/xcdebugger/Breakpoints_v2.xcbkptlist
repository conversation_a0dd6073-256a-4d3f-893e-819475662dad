<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "E8C7BF2D-6664-402E-AC90-6C89E94D046C"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F1B2008E-F55B-494C-94A1-B190FF05D9F8"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Video/Player/MusicPanelView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "763"
            endingLineNumber = "763"
            landmarkName = "startDownload(for:at:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.ExceptionBreakpoint">
         <BreakpointContent
            uuid = "B4F619B3-720E-4990-8278-5A5CA7A13D3F"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            breakpointStackSelectionBehavior = "1"
            scope = "1"
            stopOnStyle = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.SymbolicBreakpoint">
         <BreakpointContent
            uuid = "DAEACA6A-8F0B-4C3F-881C-CE5502E2FDCC"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            symbolName = "UIViewAlertForUnsatisfiableConstraints"
            moduleName = "">
            <Locations>
               <Location
                  uuid = "DAEACA6A-8F0B-4C3F-881C-CE5502E2FDCC - ba3ad5676a792f38"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "UIViewAlertForUnsatisfiableConstraints"
                  moduleName = "UIKitCore"
                  usesParentBreakpointCondition = "Yes">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "FF54B9C7-5319-4009-A708-F0EA9FFAD93F"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Setting/UserInfoEdit/UserInformationEditingPage.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "932"
            endingLineNumber = "932"
            landmarkName = "init(style:reuseIdentifier:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "84F1EBB2-3611-4E9F-BC3D-5D09042787DA"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Setting/UserInfoEdit/UserInfoEditOccupationViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "35"
            endingLineNumber = "35"
            landmarkName = "backgroundView"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7AF4685F-EC2D-46E1-A3EA-CD5D4BBB3F99"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Setting/UserInfoEdit/YearPickerPopupView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "22"
            endingLineNumber = "22"
            landmarkName = "init(selectedYear:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "72FCBD64-DBE0-401A-9EDB-1E728B81D8CF"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Me/MyCollectionViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "492"
            endingLineNumber = "492"
            landmarkName = "showCleanupConfirmAlert()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F22D70E3-F932-43AE-BD61-D7D92666DD90"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Pods/HXPhotoPicker/Sources/HXPhotoPicker/Editor/Controller/EditorMusicListViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "589"
            endingLineNumber = "589"
            landmarkName = "layoutSubviews()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6C6E25CA-7525-4E03-AE2C-9EF283D9E212"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Account/LoginViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "284"
            endingLineNumber = "284"
            landmarkName = "DesignSpec"
            landmarkType = "14">
            <Locations>
               <Location
                  uuid = "6C6E25CA-7525-4E03-AE2C-9EF283D9E212 - c1a2a122adedda41"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "one-time initialization function for totalHeight"
                  moduleName = "Shuxiaoqi.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Downloads/Shuxiaoqi/Shuxiaoqi/Modules/Controller/Account/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "284"
                  endingLineNumber = "284">
               </Location>
               <Location
                  uuid = "6C6E25CA-7525-4E03-AE2C-9EF283D9E212 - dac6f6164c20da3d"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "Shuxiaoqi.LoginViewController.DesignSpec.totalHeight.unsafeMutableAddressor : CoreGraphics.CGFloat"
                  moduleName = "Shuxiaoqi.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Downloads/Shuxiaoqi/Shuxiaoqi/Modules/Controller/Account/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "284"
                  endingLineNumber = "284">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5D5B5A31-1BCD-45A8-A1F5-BACFB58ACC08"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Modules/Controller/Video/Upload/VideoEditingDetailsViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1474"
            endingLineNumber = "1474"
            landmarkName = "applyPrefillInfo()"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "5D5B5A31-1BCD-45A8-A1F5-BACFB58ACC08 - eb5e4bef8e121edf"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "Shuxiaoqi.VideoEditingDetailsViewController.applyPrefillInfo() -&gt; ()"
                  moduleName = "Shuxiaoqi.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Downloads/Shuxiaoqi/Shuxiaoqi/Modules/Controller/Video/Upload/VideoEditingDetailsViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "1474"
                  endingLineNumber = "1474">
               </Location>
               <Location
                  uuid = "5D5B5A31-1BCD-45A8-A1F5-BACFB58ACC08 - eb5e4bef8e121edf"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "Shuxiaoqi.VideoEditingDetailsViewController.applyPrefillInfo() -&gt; ()"
                  moduleName = "Shuxiaoqi.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Downloads/Shuxiaoqi/Shuxiaoqi/Modules/Controller/Video/Upload/VideoEditingDetailsViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "1474"
                  endingLineNumber = "1474">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7988D2A2-1ADB-4604-BFF7-50A1629E2B91"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Base/CustomTabBarController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "394"
            endingLineNumber = "394"
            landmarkName = "handleShareNotification(_:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "7988D2A2-1ADB-4604-BFF7-50A1629E2B91 - d75b3afb97af2a93"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 () -&gt; () in Shuxiaoqi.CustomTabBarController.handleShareNotification(Foundation.Notification) -&gt; ()"
                  moduleName = "Shuxiaoqi.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Downloads/Shuxiaoqi/Shuxiaoqi/Base/CustomTabBarController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "394"
                  endingLineNumber = "394">
               </Location>
               <Location
                  uuid = "7988D2A2-1ADB-4604-BFF7-50A1629E2B91 - d75b3afb97af2a93"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 () -&gt; () in Shuxiaoqi.CustomTabBarController.handleShareNotification(Foundation.Notification) -&gt; ()"
                  moduleName = "Shuxiaoqi.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Downloads/Shuxiaoqi/Shuxiaoqi/Base/CustomTabBarController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "394"
                  endingLineNumber = "394">
               </Location>
               <Location
                  uuid = "7988D2A2-1ADB-4604-BFF7-50A1629E2B91 - d75b3afb97af2a93"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 () -&gt; () in Shuxiaoqi.CustomTabBarController.handleShareNotification(Foundation.Notification) -&gt; ()"
                  moduleName = "Shuxiaoqi.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Downloads/Shuxiaoqi/Shuxiaoqi/Base/CustomTabBarController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "394"
                  endingLineNumber = "394">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "66CAABAC-A2D2-4AFD-A1F0-81BFC6396FA6"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Base/CustomTabBarController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "408"
            endingLineNumber = "408"
            landmarkName = "handleShareNotification(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "********-0ADD-41B0-8257-FEAABEF6C812"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Shuxiaoqi/Base/CustomTabBarController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "396"
            endingLineNumber = "396"
            landmarkName = "handleShareNotification(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
